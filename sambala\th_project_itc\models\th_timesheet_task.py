from odoo.tools import float_round
from odoo import models, fields, api,_

class ThTimesheetTask(models.Model):
    _name = "th.timesheet.task"
    _description = "<PERSON>ấm công nhiệm vụ"

    name = fields.Char(string='Tên')
    th_user_id = fields.Many2one('res.users', string='Nhân viên')
    create_date = fields.Datetime("Ngày")
    description = fields.Char(string='Mô tả')
    th_check_group_admin = fields.Boolean(default=True,compute='_compute_check_group_admin')
    th_time_spent = fields.Float(string='Giờ đã dùng')
    th_project_task_id = fields.Many2one('th.project.task', string='Nhiệm vụ', readonly= True)

    @api.depends('th_time_spent')
    def _compute_check_group_admin(self):
        for record in self:
            if record.env.user.has_group('th_project_itc.th_group_project_manager'):
                record.th_check_group_admin = True
            else:
                record.th_check_group_admin = False

    # @api.model
    # def create(self, values):
    #     rec = super(ThTimesheetTask, self).create(values)
    #     parent_value = values
    #     parent_value['th_project_task_id'] = rec.th_project_task_id.parent_id.id
    #     parent_value['description'] = rec.th_project_task_id.name
    #     parent_value['th_time_spent'] = rec.th_time_spent
    #
    #
    #     task = rec.th_project_task_id.parent_id.write({
    #         'th_timesheet_task_ids': [(0, 0, parent_value)]
    #     })
    #
    #     return rec

    @api.model_create_multi
    def create(self, values_list):
        recs = super().create(values_list)

        for rec, values in zip(recs, values_list):
            parent_task = rec.th_project_task_id.parent_id
            if parent_task:
                parent_value = values.copy()
                parent_value.update({
                    'th_project_task_id': parent_task.id,
                    'description': rec.th_project_task_id.name,
                    'th_time_spent': rec.th_time_spent,
                })

                parent_task.write({
                    'th_timesheet_task_ids': [(0, 0, parent_value)]
                })
        print("run")
        return recs
