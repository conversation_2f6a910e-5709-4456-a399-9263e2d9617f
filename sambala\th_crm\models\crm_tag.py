from odoo import fields, models, api


class CrmTag(models.Model):
    _inherit = "crm.tag"

    th_lead_ids = fields.Many2many(comodel_name="crm.lead", relation="th_crm_tag_rel",
                                   column1="th_tag_id", column2="th_lead_id",
                                   string="<PERSON><PERSON> hội", domain="[('th_storage', '=', False),('type','=','opportunity')]")

    # @api.model
    # def create(self, values):
    #     # Add code here
    #     res = super(CrmTag, self).create(values)
    #     if values.get('th_lead_ids'):
    #         res.th_write_tags()
    #     return res

    def th_write_tags(self):
        for rec in self:
            rec.th_lead_ids.write({'tag_ids': [(4, rec.id)]})

    @api.model_create_multi
    def create(self, vals_list):
        records = super(CrmTag, self).create(vals_list)
        for record, vals in zip(records, vals_list):
            if vals.get('th_lead_ids'):
                record.th_write_tags()
        return records


    def write(self, values):
        # Add code here
        res = super(CrmTag, self).write(values)
        if values.get('th_lead_ids'):
            for rec in self:
                rec.th_write_tags()
        return res