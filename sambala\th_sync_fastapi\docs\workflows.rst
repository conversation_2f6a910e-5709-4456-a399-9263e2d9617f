==============
Chi tiết chức năng
==============

A. <PERSON>ế thừa bảng hiện tại và bảng chung
============================
1.1 Luồng hoạt động
    * Khai báo model kế thừa bảng hiện tại và bảng chung của module Th Setup Parameters nhằm mục đích mở rộng và lấy được các hàm trong model kế thừa.

1.2 Một số hàm quan trọng
    * _find_internal_id
    * _find_external_id
    * th_func_create_or_update
    * th_func_delete

B. Map ID
============================
1.1 Luồng hoạt động
    * Lấy giá trị ID của bản ghi hiện tại trên Sambala để tìm ID bản ghi tương ứng của hệ thống bên ngoài trong bảng Map ID của module Th Setup Parameters.

1.2 Một số hàm quan trọng
    * get_data_sync

C. Trigger đồng bộ dữ liệu
============================
1.1 Luồng hoạt động
    * Xây dựng sẵn các bản ghi data bằng XML trong bảng Base Automation để tự động nhảy vào logic hàm đồng bộ mỗi khi có sự thay đổi ở các trường thông tin trong model đã cấu hình.

1.2 Một số hàm quan trọng
    * th_trigger_apm_trait
    * th_trigger_apm_trait_value
    * th_trigger_apm_contact_trait
    * th_trigger_crm_stage
    * th_trigger_internship_conditions
    * th_trigger_exempted_subject
    * th_trigger_subject_registration_status
    * th_trigger_th_student_status
    * th_trigger_th_student_status_detail
    * th_trigger_country_district
    * th_trigger_country_ward
    * th_trigger_country_state
    * th_trigger_crm_lead
    * th_trigger_crm_lead_daily_sync
    * th_trigger_crm_tag
    * th_trigger_mail_message
    * th_trigger_update_contact
    * th_trigger_student_profile
    * th_trigger_apm_lead
    * th_trigger_apm_campaign
    * th_trigger_apm_level
    * th_trigger_apm_team
    * th_trigger_sale_order
    * th_trigger_source_group
    * th_trigger_graduation_system
    * th_trigger_major
    * th_trigger_admissions_station
    * th_trigger_admissions_region
    * th_trigger_training_system
    * th_trigger_origin
    * th_trigger_dividing_ring
    * th_trigger_product_category
    * th_trigger_product_template
    * th_trigger_product_pricelist
    * th_trigger_product_pricelist_item
    * th_trigger_status_particular
    * th_trigger_fee_status
    * th_trigger_student

D. Bảng tạm
============================
1.1 Luồng hoạt động
    * Kế thừa và mở rộng Bảng tạm trong module Th Setup Parameters.
    * Truyền vào Endpoint và xử lý logic riêng cho các model để đồng bộ dữ liệu.
    * Cấu hình logic schedule và xây dựng sẵn các bản ghi data bằng XML trong bảng Ir Cron để đồng bộ dữ liệu.

1.2 Một số hàm quan trọng
    * sync_fastapi
    * get_config_router
    * call_api_sync
    * schedule_sync_fastapi
    * schedule_sync_fastapi_a_day
    * _process_crm_lead
    * _process_crm_lead
    * _process_th_student
    * _process_product_template
    * _process_th_apm
    * _process_sale_order
    * _process_pricelist
