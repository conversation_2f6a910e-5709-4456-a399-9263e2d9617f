from odoo import models, fields, api, exceptions, _
import json
from datetime import date


class LearningPeriod(models.Model):
    _name = "th.learning.period"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "<PERSON><PERSON><PERSON>"

    name = fields.Char('Tên', required=1)
    th_status = fields.Selection([('draft', 'Nháp'), ('created_ticket', 'Đ<PERSON> tạo phiếu học tập'), ('complete', '<PERSON>àn thành')],
                                 string='Trạng thái', default='draft', copy=False)
    th_user_id = fields.Many2one('res.users', string="Người T<PERSON>ự<PERSON>", required=1, default=lambda self: self.env.user.id)
    th_user_team_id = fields.Many2one(comodel_name="th.srm.team", string="Đội nhóm người thực hiện",store=True)
    th_start_date = fields.Date('<PERSON><PERSON><PERSON>', required=True)
    th_end_date = fields.Date('<PERSON><PERSON><PERSON>')
    th_origin_id = fields.Many2one('th.origin', string='Trường', required=True)
    th_major_id = fields.Many2many('th.major', string='Ngành')
    th_number_contact = fields.Integer('Số sinh viên tìm được', compute="_compute_th_number_contact", default=0)
    th_domain_major = fields.Char(compute="_compute_major_id_domain")
    th_team_user = fields.Selection(string="Chia cơ hội", selection=[('team', 'Đội nhóm'), ('individual', 'Cá nhân')],
                                    default="team")
    th_individual_id = fields.Many2one(comodel_name="res.users", string="Cá nhân")
    th_team_id = fields.Many2one(comodel_name="th.srm.team", string="Đội nhóm")
    th_ballot_type = fields.Many2one('th.ballot.type', string='Loại phiếu', required=1)
    th_learning_ticket_ids = fields.One2many(comodel_name='th.learning.ticket',inverse_name='th_learning_period_id', string='Phiếu học tập')
    learning_ticket_count = fields.Integer(compute='_compute_learning_ticket_count')
    th_list_student_selected = fields.Many2many('th.student')

    @api.depends('th_list_student_selected')
    def _compute_th_number_contact(self):
        for rec in self:
            if rec.th_list_student_selected:
                rec.th_number_contact = len(rec.th_list_student_selected)
            else:
                rec.th_number_contact = 0

    # @api.depends('th_user_id')
    # def _compute_th_user_team_id(self):
    #     for rec in self:
    #         if rec.th_user_id:
    #             team_user = self.env['th.srm.team'].search([('member_ids','in',rec.th_user_id.ids)])
    #             if team_user:
    #                 rec.th_user_team_id = team_user
    #             else:
    #                 rec.th_user_team_id = False

    @api.depends('th_origin_id')
    def _compute_major_id_domain(self):
        for rec in self:
            domain = []
            if rec.th_origin_id:
                th_origin = self.env['th.university.major'].search([('th_origin_id', '=', rec.th_origin_id.id)]).mapped(
                    'th_major_id').ids
                domain.append(('id', 'in', th_origin))
            rec.th_domain_major = json.dumps(domain)

    @api.onchange('th_team_user')
    def _onchange_team_or_user(self):
        if self.th_team_user == 'team':
            self.th_individual_id = False
        if self.th_team_user == 'individual':
            self.th_team_id = False

    def action_create_learning_ticket(self):
        # if self.th_major_id.ids:
        #     exist_partner = self.env['th.student'].search([('th_origin_id', '=', self.th_origin_id.id),
        #                                                   ('th_major_id', 'in', self.th_major_id.ids),
        #                                                   ('th_semester', '!=', False)])
        # else:
        #     exist_partner = self.env['th.student'].search([('th_origin_id', '=', self.th_origin_id.id),
        #                                                   ('th_semester', '!=', False)])
        if self.th_list_student_selected:
            for rec in self.th_list_student_selected:
                val_list = {'th_learning_period_id': self.id,
                            'name': self.name,
                            'th_partner_id': rec.id,
                            'th_partner_email': rec.th_partner_email,
                            'th_partner_phone': rec.th_partner_phone,
                            'th_code': rec.th_code,
                            'th_student_code': rec.th_student_code,
                            'th_class': rec.th_class,
                            'th_ballot_type': self.th_ballot_type.id,
                            'th_care_schedule_date_from': self.th_start_date,
                            'th_care_schedule_date_to': self.th_end_date,
                            'th_status': rec.th_status,
                            'th_student_status_particular': rec.th_student_status_particular.id,
                            'th_status_category_id': rec.th_status_category_id.id,
                            'th_internship_detail_id': rec.th_internship_detail_id.id,
                            'th_exempted_sub': rec.th_exempted_sub,
                            'th_exempted_sub_detail_id': rec.th_exempted_sub_detail_id.id,
                            'th_study_result_ids': [[6, 0, rec.th_study_result_ids.ids]],
                            'th_care_schedule_of_defer': rec.th_care_schedule_of_defer.id,
                            'th_reason_stop_learning': rec.th_reason_stop_learning,
                            'th_stop_learning_date': rec.th_stop_learning_date,
                            'is_vstep': rec.is_vstep,
                            'th_status_vstep': rec.th_status_vstep,
                            'th_on_learning_vstep': rec.th_on_learning_vstep,
                            'th_stop_learning_vstep': rec.th_stop_learning_vstep,
                            'th_completed_learning_vstep': rec.th_completed_learning_vstep
                            }
                if self.th_team_user == 'individual':
                    val_list['th_user_id'] = self.th_individual_id.id
                elif self.th_team_user == 'team':
                    th_team_id = self._context.get('th_team_id') or self.th_team_id
                    member_ids = self.th_team_id.member_ids.ids
                    member_ids.sort()
                    if len(member_ids) == 0:
                        raise exceptions.UserError(
                            _("Đội/Nhóm '%s' chưa có thành viên. Vui lòng thêm thành viên vào nhóm để thực hiện chức năng này",
                              self.th_team_id.name))
                    th_flag = json.loads(self.th_team_id.th_flag)
                    flag = th_flag[0]
                    val_list['th_user_id'] = member_ids[flag]
                    flag = (flag + 1) % len(member_ids)
                    th_team_id.th_flag = json.dumps([flag, member_ids[flag]])
                rec = self.env['th.learning.ticket'].sudo().create(val_list)
                if rec:
                    self.sudo().write({'th_status': 'created_ticket'})
        # return rec

    def action_check_expiration_date_of_learning_period(self):
        for rec in self:
            if rec.th_end_date < date.today():
                rec.th_status = 'complete'

    def action_view_details_students_find(self):
        exist_partner = []
        if self.th_origin_id:
            exist_partner = self.env['th.student'].search(
                [('th_origin_id', '=', self.th_origin_id.id), ('th_semester', '!=', False)])
            if self.th_major_id:
                exist_partner = self.env['th.student'].search(
                    [('th_origin_id', '=', self.th_origin_id.id), ('th_major_id', 'in', self.th_major_id.ids),
                     ('th_semester', '!=', 0)])
            # domain = exist_partner
        return {
            'name': _('View details students find'),
            'view_mode': 'tree',
            'res_model': 'th.student',
            'view_id': self.env.ref('th_srm.th_student_view_tree').id,
            'type': 'ir.actions.act_window',
            'context': {'from_learning_period': True,'create': False,},
            'target': 'new',
            'domain': [('id', 'in', exist_partner.ids)],
        }

    def action_view_learning_ticket(self):
        self.ensure_one()
        return {
            'name': 'Phiếu Chăm Sóc Học Tập',
            'view_mode': 'tree,form',
            'res_model': 'th.learning.ticket',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('th_learning_period_id', '=', self.id)],
        }

    def _compute_learning_ticket_count(self):
        for rec in self:
            rec.learning_ticket_count = self.env['th.learning.ticket'].search_count([('th_learning_period_id', '=', rec.id)])

    @api.onchange('th_origin_id')
    def onchange_major_by_school(self):
        if self.th_major_id:
            self.th_major_id = False


class LearningCare(models.Model):
    _name = "th.learning.ticket"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Phiếu chăm sóc học tập"
    _order = "th_last_check desc"

    name = fields.Char('Tên phiếu')
    th_partner_id = fields.Many2one('th.student', string='Sinh Viên', required=1)
    th_partner_email = fields.Char(string="Email", related="th_partner_id.th_partner_email", tracking=True, store=True)
    th_partner_phone = fields.Char(string="Điện thoại", related="th_partner_id.th_partner_phone", store=True)
    th_code = fields.Char(string='Mã Sinh Viên (AUM)', related="th_partner_id.th_code", store=True)
    th_student_code = fields.Char(string='Mã sinh viên (Trường)', related="th_partner_id.th_student_code", store=True)
    th_origin_id = fields.Many2one(string='Trường', related="th_partner_id.th_origin_id", store=True)
    th_major_id = fields.Many2one(string='Ngành', related="th_partner_id.th_major_id", store=True)
    th_class = fields.Char(string='Khóa', related="th_partner_id.th_class", store=True)
    th_user_id = fields.Many2one('res.users', string='Người Chăm Sóc SRM', tracking=True)
    state = fields.Selection(
        selection=[('receive', 'Tiếp nhận'),
                   ('handle', 'Xử lý'),
                   ('cancel', 'Hủy bỏ'),
                   ('completed', 'Hoàn thành')],
        string="Trạng thái",
        default='receive', tracking=True
    )
    th_care_schedule_date_from = fields.Date('Lịch Chăm Sóc Từ Ngày')
    th_care_schedule_date_to = fields.Date('Lịch Chăm Sóc Đến Ngày')
    th_ballot_type = fields.Many2one('th.ballot.type', string='Loại phiếu')
    th_learning_period_id = fields.Many2one(comodel_name='th.learning.period', string='Đợt học tập', ondelete='restrict')
    th_create_period = fields.Many2one(related='th_learning_period_id.th_user_id', store=True)
    th_status_status_id = fields.Many2one(string="Tình trạng gọi", related='th_partner_id.th_status_category_id', store=True)
    th_status_status_detail_id = fields.Many2one(related='th_partner_id.th_status_status_detail_id',
                                                 string="Tình trạng chi tiết", store=True)
    th_learning_status_id = fields.Many2one('th.student.status', string='Tình trạng học tập', tracking=True,
                                            related='th_partner_id.th_learning_status_id', store=True)
    th_status_detail_id = fields.Many2one('th.student.status.detail', string='Tình Trạng học tập chi tiết',
                                          tracking=True, related='th_partner_id.th_status_detail_id', store=True)
    th_manager_of_user_id_learning = fields.Many2one(comodel_name='res.users', string='Đội trưởng của người chăm sóc',
                                             store=True)
    th_manager_of_create_period_learning = fields.Many2one(comodel_name='res.users', string='Đội trưởng của người tạo phiếu',
                                                   store=True)
    name_id_sequence = fields.Char(copy=False)
    th_team_of_user_id = fields.Many2one('th.srm.team', string='Đội của người chăm sóc', store=True)
    th_team_of_create_period_id = fields.Many2one('th.srm.team', string='Đội của người tạo phiếu', store=True)
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", default=lambda self: fields.Datetime.now(), tracking=True)
    th_birthday = fields.Date(string="Ngày sinh", related="th_partner_id.th_birthday", store=True)
    th_specialization_class = fields.Char('Lớp chuyên ngành', related="th_partner_id.th_specialization_class", store=True)
    th_status = fields.Selection(
        [('on_learning', 'Đang Học'), ('stop_learning', 'Ngưng Học'),
         ('graduated', 'Tốt Nghiệp'), ('defer', 'Bảo Lưu'),
         ('close_station', 'Đóng trạm'), ('finish_course', 'Kết Thúc Khóa Học')], string='Tình Trạng Sinh Viên', tracking=True)
    th_stop_learning_particular = fields.Selection([('stop_learning_decided', 'Ngưng Học Đã Có Quyết Định'),
                                                    ('stop_learning_undecided', 'Ngưng Học Chưa Có Quyết Định')],
                                                   string='Tình Trạng Ngưng Học Chi Tiết')
    is_vstep = fields.Boolean(string="VSTEP", store=True)
    th_status_vstep = fields.Selection(
        [('on_learning', 'Đang Học'), ('stop_learning', 'Ngưng Học'), ('completed', 'Kết thúc khóa học')],
        string='Tình Trạng Sinh Viên Vstep', tracking=True)
    th_on_learning_vstep = fields.Selection([('learning_normal', 'Bình thường'),
                                             ('learning_risk', 'Nguy cơ')],
                                            string='Tình Trạng Đang Học Chi Tiết')
    th_stop_learning_vstep = fields.Selection([('stop_learning_by_student', 'Ngưng Học Do Vấn Đề Học Viên'),
                                               ('stop_learning_by_system', 'Ngưng Học Do Hệ Thống, Học Liệu'),
                                               ('stop_learning_by_other', 'Ngưng Học Do Các Vấn Đề Hỗ Trợ')],
                                              string='Tình Trạng Ngưng Học Chi Tiết')
    th_completed_learning_vstep = fields.Selection([('completed_learning', 'Hoàn thành khóa học'),
                                                    ('uncompleted_learning', 'Chưa hoàn thành khóa học')],
                                                   string='Tình Trạng Kết Thúc Khóa Học Chi Tiết')
    th_student_status_particular = fields.Many2one('th.status.student.particular',
                                                   string='Tình trạng chi tiết sinh viên', tracking=True)
    th_status_category_id = fields.Many2one(string="Tình trạng gọi", comodel_name="th.status.category",
                                            domain="[('th_type', '=?', 'srm')]", tracking=True)
    th_internship_detail_id = fields.Many2one('th.internship.conditions', string='Điều kiện thực tập chi tiết',
                                              tracking=True)
    th_exempted_sub = fields.Char('Tình trạng miễn môn', tracking=True)
    th_exempted_sub_detail_id = fields.Many2one('th.exempted.subject', string='Tình trạng miễn môn chi tiết',
                                                tracking=True)
    th_study_result_ids = fields.One2many('th.study.result', 'th_learning_ticket_id', string='Kết Quả Học Tập', tracking=True)
    th_care_schedule_of_defer = fields.Many2one('th.care.schedule.of.defer', string='Lịch chăm sóc bảo lưu/ngừng học')
    th_reason_stop_learning = fields.Char('Lý do ngưng học')
    th_stop_learning_date = fields.Date('Ngày Ngưng Học', tracking=True)
    th_domain_student_status_particular = fields.Char(compute="student_status_particular_domain")
    th_opportunity_code_crm = fields.Char(string='Mã cơ hội', related="th_partner_id.th_opportunity_code_crm",
                                          store=True)
    th_customer_code_aum = fields.Char(string='Mã khách hàng', related="th_partner_id.th_customer_code_aum", store=True)

    @api.onchange('th_status_status_id')
    def onchange_th_call_status(self):
        if self.th_status_status_detail_id and self.th_status_status_detail_id.id not in self.th_status_status_id.th_status_detail_ids.ids:
            self.th_status_status_detail_id = False

    # @api.depends('th_user_id', 'th_create_period')
    # def _compute_th_team_leader_ids_learning(self):
    #     for rec in self:
    #         rec.th_manager_of_user_id_learning = False
    #         rec.th_manager_of_create_period_learning = False
    #         if rec.th_user_id:
    #             rec.th_team_of_user_id = self.env['th.srm.team'].search([('member_ids', 'in', rec.th_user_id.ids)])
    #             rec.th_team_of_create_period_id = self.env['th.srm.team'].search([('member_ids', 'in', rec.th_create_period.ids)])

                # rec.th_manager_of_user_id_learning = parent_team_user_id.user_id.id
                # rec.th_manager_of_create_period_learning = parent_team_create_period.user_id.id

    # @api.model
    # def create(self, vals_list):
    #     vals_list['name_id_sequence'] = self.env['ir.sequence'].next_by_code('th.learning.ticket')
    #     vals_list['name'] = "[SRM.HT" + vals_list['name_id_sequence'] + "]" + "-" + vals_list['name']
    #     res = super(LearningCare, self).create(vals_list)
    #     return res

    @api.model_create_multi
    def create(self, values_list):
        for values in values_list:
            values['name_id_sequence'] = self.env['ir.sequence'].next_by_code('th.learning.ticket')
            values['name'] = "[SRM.HT" + values['name_id_sequence'] + "]" + "-" + values['name']
        res = super().create(values_list)
        return res

    def write(self, values):
        for rec in self:
            if values.get('th_status'):
                rec.sudo().th_partner_id.th_status = values.get('th_status')
                if values['th_status'] == 'stop_learning':
                    values['th_stop_learning_date'] = fields.Date.today()
                elif values['th_status'] != 'stop_learning':
                    values['th_stop_learning_date'] = False
            if values.get('th_student_status_particular'):
                rec.sudo().th_partner_id.th_student_status_particular = values.get('th_student_status_particular')
            if values.get('th_status_category_id'):
                rec.sudo().th_partner_id.th_status_category_id = values.get('th_status_category_id')
            if values.get('th_internship_detail_id'):
                rec.sudo().th_partner_id.th_internship_detail_id = values.get('th_internship_detail_id')
            if values.get('th_exempted_sub'):
                rec.sudo().th_partner_id.th_exempted_sub = values.get('th_exempted_sub')
            if values.get('th_exempted_sub_detail_id'):
                rec.sudo().th_partner_id.th_exempted_sub_detail_id = values.get('th_exempted_sub_detail_id')
            if values.get('th_care_schedule_of_defer'):
                rec.sudo().th_partner_id.th_care_schedule_of_defer = values.get('th_care_schedule_of_defer')
            if values.get('th_reason_stop_learning'):
                rec.sudo().th_partner_id.th_reason_stop_learning = values.get('th_reason_stop_learning')
            if values.get('th_stop_learning_date'):
                rec.sudo().th_partner_id.th_stop_learning_date = values.get('th_stop_learning_date')
        return super(LearningCare, self).write(values)

    @api.depends('th_status','th_stop_learning_particular')
    def student_status_particular_domain(self):
        for rec in self:
            domain = []
            if rec.th_status:
                student_status_particular = self.env['th.status.student.particular'].search(
                    [('th_status', '=', rec.th_status)]).ids
                if rec.th_stop_learning_particular:
                    student_status_particular = self.env['th.status.student.particular'].search(
                    [('th_stop_learning_particular', '=', rec.th_stop_learning_particular)]).ids
                domain.append(('id', 'in', student_status_particular))
            rec.th_domain_student_status_particular = json.dumps(domain)