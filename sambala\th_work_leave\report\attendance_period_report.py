# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from datetime import timedelta, datetime, date
from dateutil.relativedelta import relativedelta
from odoo.addons.resource.models.resource import float_to_time
from decimal import Decimal, ROUND_HALF_UP


class AttendancePeriodWizard(models.TransientModel):
    _name = 'report.th_attendance.attendance_period_report'
    _description = "Attendance Period Report Xlsx"
    _inherit = 'report.report_xlsx.abstract'

    employee_id = fields.Many2one('hr.employee', 'Employee', required=True)
    date_from = fields.Date('Date from', required=True)
    date_to = fields.Date('Date to', required=True)

    # @api.model
    # def create(self, vals):
    #     self.sudo().env.ref("th_work_leave.ir_cron_th_attendance_auto_vacuum_invalid_attendance").method_direct_trigger()
    #     return super(AttendancePeriodWizard, self).create(vals)

    # @api.model_create_multi
    # def create(self, values_list):
    #     # <PERSON><PERSON><PERSON> cron job một lần tr<PERSON><PERSON><PERSON> khi tạo bản ghi
    #     self.sudo().env.ref(
    #         "th_work_leave.ir_cron_th_attendance_auto_vacuum_invalid_attendance").method_direct_trigger()
    #
    #     # Tạo tất cả bản ghi cùng lúc
    #     return super().create(values_list)

    def name_get(self):
        res = []
        for rec in self:
            res.append((rec.id, f"{rec.employee_id.name}"))
        return res

    # def action_generate_pdf_report(self):
    #     attendance_ids = self.sudo().employee_id.attendance_ids.filtered(lambda l: self.date_from <= l.th_attendance_date <= self.date_to)
    #     datas = {
    #         'employee_name': self.employee_id.name,
    #         'date_from': self.date_from.strftime('%d/%m/%Y'),
    #         'date_to': self.date_to.strftime('%d/%m/%Y'),
    #         'attendances': [],
    #         'docs': self,
    #     }
    #     for attendance in reversed(attendance_ids):
    #         add_time = attendance.employee_id.th_spec_att_id.th_time if attendance.employee_id.th_spec_att_id else False
    #         add_number = True if attendance.employee_id.th_attendance_type and attendance.employee_id.th_attendance_type == "two_times" else False
    #         th_work_numbers = attendance.th_work_numbers
    #         count_employee = attendance.sudo().search_count([('th_attendance_date', '=', attendance.th_attendance_date), ('employee_id', '=', attendance.employee_id.id)])
    #         if add_time and not add_number:
    #             round_work_time = th_work_numbers + 30 / 480 if th_work_numbers + 30 / 480 <= 0.5 and th_work_numbers < 0.5 else 0.5
    #         elif add_time and add_number:
    #             if th_work_numbers < 0.5 and count_employee >= 1:
    #                 round_work_time = th_work_numbers + 30 / 480 if th_work_numbers + 30 / 480 <= 0.5 and th_work_numbers < 0.5 else 0.5
    #
    #             elif 0.5 <= th_work_numbers <= 1 <= count_employee:
    #                 round_work_time = th_work_numbers + 30 / 480 if th_work_numbers + 30 / 480 > 0.5 and 0.5 < th_work_numbers < 1 else 1
    #         else:
    #             round_work_time = th_work_numbers
    #
    #         datas['attendances'].append({
    #             'date': attendance.th_attendance_date.strftime('%d/%m/%Y'),
    #             'check_in': (attendance.check_in + timedelta(hours=7)).strftime('%H:%M') if attendance.check_in else "",
    #             'check_out': (attendance.check_out + timedelta(hours=7)).strftime('%H:%M') if attendance.check_out else "",
    #             'work_numbers': round(round_work_time)
    #         })
    #     return self.env.ref('th_work_leave.th_employee_attendance_period_report').report_action(self, data=datas)

    def generate_xlsx_report(self, workbook, data, partners):
        attendance_timekeeper_request = workbook.add_worksheet('Dữ liệu công')
        attendance_online_request = workbook.add_worksheet('Công online')
        time_off_sheet = workbook.add_worksheet('Dữ liệu nghỉ phép')
        attendance_request_sheet = workbook.add_worksheet('Dữ liệu bù công')

        self.generate_attendance_timekeeper_request(workbook, data, partners, attendance_timekeeper_request)
        self.generate_attendance_attendance_online_request(workbook, data, partners, attendance_online_request)
        self.generate_time_off_sheet(workbook, data, partners, time_off_sheet)
        self.generate_attendance_request_sheet(workbook, data, partners, attendance_request_sheet)

    def generate_attendance_timekeeper_request(self, workbook, data, partners, worksheet):
        header_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'border': 1})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': 'dd/mm/yyyy', 'border': 1})
        normal_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': '@', 'border': 1})
        time_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': 'hh:mm', 'border': 1})

        # Generate Header
        worksheet.merge_range("B2:H2", f"Phiếu điểm danh của {partners.employee_id.name} Từ {partners.date_from.strftime('%d/%m/%Y')} đến {partners.date_to.strftime('%d/%m/%Y')}" , header_format)
        worksheet.write(3, 1, 'STT', header_format)
        worksheet.write(3, 2, 'Thời gian', header_format)
        worksheet.write(3, 3, 'Vào làm', header_format)
        worksheet.write(3, 4, 'Tan làm', header_format)
        worksheet.write(3, 5, 'Ngày', header_format)
        worksheet.write(3, 6, 'Thời gian làm tròn (phút)', header_format)
        worksheet.write(3, 7, 'Công đã làm tròn', header_format)

        # Generate Data
        total = 0
        sunday = 6
        saturday = 5
        sequence = 1
        round_total = 0
        start_row_index = 4
        sunday_half_am, sunday_half_pm, saturday_all, sunday_all = [], [], [], []

        wf_leaves = self.env['hr.leave'].get_work_days_compensate_for_holidays(partners.date_from)
        for wf_leave in wf_leaves:
            if wf_leave['th_wfl_work_date'].weekday() == saturday and wf_leave['th_wfl_select_session'] != 'am':
                saturday_all.append(wf_leave['th_wfl_work_date'])
            if wf_leave['th_wfl_work_date'].weekday() == sunday:
                if wf_leave['th_wfl_select_session'] == 'all':
                    sunday_all.append(wf_leave['th_wfl_work_date'])
                if wf_leave['th_wfl_select_session'] == 'pm':
                    sunday_half_pm.append(wf_leave['th_wfl_work_date'])
                if wf_leave['th_wfl_select_session'] == 'am':
                    sunday_half_am.append(wf_leave['th_wfl_work_date'])

        th_date = partners.date_from
        while th_date <= partners.date_to:
            max_day = 1
            # Nếu thứ 7 + 0.5
            # Nếu thứ 7 có bù(sáng) hoặc ko bù và cn bù nửa ngày  + 0.5
            if ((th_date.weekday() == saturday and th_date not in saturday_all) or
                    (th_date.weekday() == sunday and th_date in sunday_half_am + sunday_half_pm)):
                max_day = 0.5

            attendance_ids = partners.sudo().employee_id.attendance_ids.filtered(lambda l: th_date == l.th_attendance_date and l.state == 'approved')
            work_day = 0
            row_marge = start_row_index
            for attendance in attendance_ids:
                if attendance.th_attendance_date.day == 7:
                    print('a')

                round_work_time = attendance.th_work_numbers
                worksheet.write(start_row_index, 1, sequence, normal_format)
                worksheet.write(start_row_index, 2, attendance.th_attendance_date, date_format)

                worksheet.write(start_row_index, 3, attendance.check_in + timedelta(hours=7) if attendance.check_in else "", time_format)
                worksheet.write(start_row_index, 4, attendance.check_out + timedelta(hours=7) if attendance.check_out else "", time_format)
                worksheet.write(start_row_index, 5, float(Decimal(round_work_time).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)), normal_format)

                start_row_index += 1
                total += float(Decimal(round_work_time).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
                work_day += round_work_time if round_work_time < 1 else 1
                sequence += 1

            th_date_accept = partners.sudo().employee_id.th_date_accept
            late_time = partners.sudo().employee_id.th_spec_att_id.th_time_is_late if partners.sudo().employee_id.th_spec_att_id else False
            time_applies = partners.sudo().employee_id.th_spec_att_id.th_time_applies if partners.sudo().employee_id.th_spec_att_id.th_time_applies else False
            add_time = partners.sudo().employee_id.th_spec_att_id.th_time if partners.sudo().employee_id.th_spec_att_id else False
            check_time = th_date_accept + relativedelta(months=time_applies) if time_applies else th_date + relativedelta(years=1)

            if work_day > 0:
                check_time_late = float(Decimal(((late_time if late_time else add_time) / (480 if max_day != 0.5 else 960))).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)) + work_day
                if attendance_ids.filtered(lambda record: record.th_att_code != 'WFH') and th_date_accept and th_date_accept <= th_date <= check_time:
                    if max_day == 0.5:
                        work_day = work_day + float(Decimal(add_time / 960).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)) if check_time_late >= 0.5 else work_day
                        work_day = work_day if work_day < 0.5 else 0.5
                    else:
                        work_day = work_day + float(Decimal(add_time / 480).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)) if check_time_late >= 1 else work_day
                        work_day = work_day if work_day < 1 else 1.0

            if len(attendance_ids) > 1:
                if attendance_ids.filtered(lambda record: record.th_att_code != 'WFH' and th_date_accept and th_date_accept <= th_date <= check_time):
                    worksheet.merge_range(row_marge, 6, (row_marge + len(attendance_ids) - 1) if len(attendance_ids) > 1 else row_marge, 6, add_time, normal_format)
                else:
                    worksheet.merge_range(row_marge, 6, (row_marge + len(attendance_ids) - 1) if len(attendance_ids) > 1 else row_marge, 6, "", normal_format)
                worksheet.merge_range(row_marge, 7, (row_marge + len(attendance_ids) - 1) if len(attendance_ids) > 1 else row_marge, 7, round(work_day, 2), normal_format)
            else:
                if attendance_ids.filtered(lambda record: th_date_accept and th_date_accept <= th_date <= check_time):
                    worksheet.write(start_row_index - 1, 6, add_time, normal_format)
                else:
                    worksheet.write(start_row_index - 1, 6, "", normal_format)
                worksheet.write(row_marge, 7, float(Decimal(work_day).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)), normal_format)

            # row_marge = start_row_index
            round_total += float(Decimal(work_day).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
            th_date = th_date + relativedelta(days=1)

        # Generate Footer
        worksheet.write(start_row_index, 1, 'Tổng số', header_format)
        worksheet.write(start_row_index, 2, '', normal_format)
        worksheet.write(start_row_index, 3, '', normal_format)
        worksheet.write(start_row_index, 4, '', normal_format)
        worksheet.write(start_row_index, 5, float(Decimal(total).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)), header_format)
        worksheet.write(start_row_index, 6, "", header_format)
        worksheet.write(start_row_index, 7, round_total, header_format)
        # Format worksheet
        worksheet.set_column("G:G", 12)
        worksheet.set_column("B:B", 6)
        worksheet.set_column("C:F", 16)

    def generate_attendance_attendance_online_request(self, workbook, data, partners, worksheet):
        attendance_ids = partners.sudo().employee_id.attendance_ids.filtered(lambda l: partners.date_from <= l.th_attendance_date <= partners.date_to and l.th_att_code == 'WFH')

        header_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'border': 1})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': 'dd/mm/yyyy', 'border': 1})
        normal_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': '@', 'border': 1})
        time_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'num_format': 'hh:mm', 'border': 1})

        # Generate Header
        worksheet.merge_range("B2:G2", f"Phiếu điểm danh online của {partners.employee_id.name} Từ {partners.date_from.strftime('%d/%m/%Y')} đến {partners.date_to.strftime('%d/%m/%Y')}" , header_format)
        worksheet.write(3, 1, 'STT', header_format)
        worksheet.write(3, 2, 'Thời gian', header_format)
        worksheet.write(3, 3, 'Vào làm', header_format)
        worksheet.write(3, 4, 'Tan làm', header_format)
        worksheet.write(3, 5, 'Công', header_format)
        worksheet.write(3, 6, 'Duyệt', header_format)

        # Generate Data
        start_row_index = 4
        total = 0
        sequence = 1
        for attendance in reversed(attendance_ids):
            worksheet.write(start_row_index, 1, sequence, normal_format)
            worksheet.write(start_row_index, 2, attendance.th_attendance_date, date_format)
            worksheet.write(start_row_index, 3, attendance.check_in + timedelta(hours=7) if attendance.check_in else "", time_format)
            worksheet.write(start_row_index, 4, attendance.check_out + timedelta(hours=7) if attendance.check_out else "", time_format)
            worksheet.write(start_row_index, 5, float(Decimal(attendance.th_work_numbers).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)), normal_format)
            worksheet.write(start_row_index, 6,  "Duyệt" if attendance.state == 'approved' else "", normal_format)
            start_row_index += 1
            total += attendance.filtered(lambda l: l.state == 'approved').th_work_numbers
            sequence += 1
        # Generate Footer
        worksheet.write(start_row_index, 1, 'Tổng số', header_format)
        worksheet.write(start_row_index, 2, '', normal_format)
        worksheet.write(start_row_index, 3, '', normal_format)
        worksheet.write(start_row_index, 4, '', normal_format)
        worksheet.write(start_row_index, 5, float(Decimal(total).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)), header_format)
        worksheet.write(start_row_index, 6, "", header_format)
        # Format worksheet
        worksheet.set_column("B:B", 6)
        worksheet.set_column("C:F", 16)

    def generate_time_off_sheet(self, workbook, data, partners, worksheet):
        header_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'border': 1})
        normal_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': '@', 'right': 1, 'bottom': 3})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'dd/mm/yyyy hh:mm', 'right': 1, 'bottom': 3, 'left':1})
        number_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': '#,##0.00', 'right': 1, 'bottom': 3})

        # Generate header
        worksheet.merge_range("B2:F2", f"Chi tiết nghỉ phép của {partners.employee_id.name} Từ {partners.date_from.strftime('%d/%m/%Y')} đến {partners.date_to.strftime('%d/%m/%Y')}" , header_format)
        worksheet.write(3, 1, 'Từ ngày', header_format)
        worksheet.write(3, 2, 'Đến ngày', header_format)
        worksheet.write(3, 3, 'Công', header_format)
        worksheet.write(3, 4, 'Ghi chú', header_format)
        worksheet.write(3, 5, 'Duyệt', header_format)

        # Generate time-off data
        paid_leave_type_ids = set(self.sudo().env['hr.leave.type'].search([('th_pay_wage', '=', True), ('requires_allocation', '=', 'yes')]).ids)
        law_leave_type_ids = set(self.sudo().env['hr.leave.type'].search([('th_pay_wage', '=', True), ('th_law_leave', '=', True), ('requires_allocation', '=', 'no')]).ids)
        unpaid_leave_type_ids = set(self.sudo().env['hr.leave.type'].search([('th_pay_wage', '=', False), ('th_law_leave', '=', False)]).ids)
        all_leave_ids = self.sudo().env['hr.leave'].search([('employee_id', '=', partners.sudo().employee_id.id),('date_from', '>=', partners.date_from), ('date_from', '<=', partners.date_to)])
        paid_leave_ids = all_leave_ids.filtered(lambda l: l.holiday_status_id.id in paid_leave_type_ids)
        law_leave_ids = all_leave_ids.filtered(lambda l: l.holiday_status_id.id in law_leave_type_ids)
        unpaid_leave_ids = all_leave_ids.filtered(lambda l: l.holiday_status_id.id in unpaid_leave_type_ids)

        start_row_index = 4
        for leave in paid_leave_ids:
            leave_status = "Duyệt" if leave.state == "validate" else ""
            worksheet.write(start_row_index, 1, leave.date_from + relativedelta(hours=7) if leave.date_from else "", date_format)
            worksheet.write(start_row_index, 2, leave.date_to + relativedelta(hours=7) if leave.date_to else "", date_format)
            worksheet.write(start_row_index, 3, leave.number_of_days if leave.number_of_days else "", number_format)
            worksheet.write(start_row_index, 4, "Phép", normal_format)
            worksheet.write(start_row_index, 5, leave_status, normal_format)
            start_row_index += 1

        for leave in law_leave_ids:
            leave_status = "Duyệt" if leave.state == "validate" else ""
            worksheet.write(start_row_index, 1, leave.date_from + relativedelta(hours=7) if leave.date_from else "", date_format)
            worksheet.write(start_row_index, 2, leave.date_to + relativedelta(hours=7) if leave.date_to else "", date_format)
            worksheet.write(start_row_index, 3, leave.number_of_days if leave.number_of_days else "", number_format)
            worksheet.write(start_row_index, 4, "Chế độ", normal_format)
            worksheet.write(start_row_index, 5, leave_status, normal_format)
            start_row_index += 1

        for leave in unpaid_leave_ids:
            leave_status = "Duyệt" if leave.state == "validate" else ""
            worksheet.write(start_row_index, 1, leave.date_from + relativedelta(hours=7) if leave.date_from else "", date_format)
            worksheet.write(start_row_index, 2, leave.date_to + relativedelta(hours=7) if leave.date_to else "", date_format)
            worksheet.write(start_row_index, 3, leave.number_of_days, number_format)
            worksheet.write(start_row_index, 4, "KL", normal_format)
            worksheet.write(start_row_index, 5, leave_status, normal_format)
            start_row_index += 1

        # Format Excel

        worksheet.set_column("B:B", 20)
        worksheet.set_column("C:C", 20)

    def generate_attendance_request_sheet(self, workbook, data, partners, worksheet):
        header_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'border': 1})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'dd/mm/yyyy', 'right': 1, 'bottom': 3})
        time_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'hh:mm', 'right': 1, 'bottom': 3})
        normal_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': '@', 'right': 1, 'bottom': 3})

        # Generate header
        worksheet.merge_range("B2:F2", f"Chi tiết điều chỉnh công {partners.employee_id.name} Từ {partners.date_from.strftime('%d/%m/%Y')} đến {partners.date_to.strftime('%d/%m/%Y')}" , header_format)
        worksheet.write(3, 1, 'Loại điều chỉnh', header_format)
        worksheet.write(3, 2, 'Loại bù công', header_format)
        worksheet.write(3, 3, 'Ngày', header_format)
        worksheet.write(3, 4, 'Thời gian', header_format)
        worksheet.write(3, 5, 'Duyệt', header_format)

        # Generate data
        start_row_index = 4
        adjustment_type = {'compensation': 'Bù công', 'excess_work': 'Thừa công',}
        request_type = {'morning_in': 'Vào sáng', 'morning_out': 'Ra sáng', 'afternoon_in': 'Vào chiều',
                        'afternoon_out': 'Ra chiều'}
        for request in self.sudo().env['th.attendance.request'].search(
                [('th_employee_id', '=', partners.sudo().employee_id.id), ('th_date_req', '>=', partners.date_from), ('th_date_req', '<=', partners.date_to)], order='th_date_req asc'):
            worksheet.write(start_row_index, 1, adjustment_type.get(request.th_attendance_type) if adjustment_type.get(request.th_attendance_type) else "", normal_format)
            worksheet.write(start_row_index, 2, request_type.get(request.th_type) if request_type.get(request.th_type) else "", normal_format)
            worksheet.write(start_row_index, 3, request.th_date_req if request.th_date_req else "", date_format)
            worksheet.write(start_row_index, 4, float_to_time(request.th_time) if float_to_time(request.th_time) and request.th_attendance_type == 'compensation' else "", time_format)
            worksheet.write(start_row_index, 5, "Duyệt" if request.th_status == "confirm" else "", normal_format)
            start_row_index += 1

        # Format Excel
        worksheet.set_column("B:D", 16)
        worksheet.set_column("E:E", 8)

    def action_generate_excel_report(self):
        return self.env.ref('th_work_leave.th_employee_attendance_period_report_xlsx').report_action(self)