==============
Request
==============

1. Đ<PERSON>ng bộ cấu hình Đặc điểm APM sang bên B2B
--------------------------------------
1. <PERSON><PERSON><PERSON><PERSON> thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * map_data_apm_trait_from_sync
    * get_data_sync
    * th_trigger_apm_trait

4. Chức năng
    * Map ID bản ghi của hệ thống hiện tại với ID bản ghi của hệ thống bên ngoài
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

2. Đồng bộ cấu hình Giá trị đặc điểm APM sang bên B2B
--------------------------------------
1. <PERSON><PERSON><PERSON><PERSON> thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_apm_trait_value

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

3. Đồng bộ cấu hình Đặc điểm liên hệ APM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * map_data_apm_contact_trait_from_sync
    * get_data_sync
    * th_trigger_apm_contact_trait

4. Chức năng
    * Map ID bản ghi của hệ thống hiện tại với ID bản ghi của hệ thống bên ngoài
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

4. Đồng bộ cấu hình Điều kiện thực tập sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_internship_conditions

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

5. Đồng bộ cấu hình Tình trạng miễn môn sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_exempted_subject

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

6. Đồng bộ cấu hình Tình trạng đăng ký môn sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_subject_registration_status

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

7. Đồng bộ cấu hình Tình Trạng Chi Tiết Học tập sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_th_student_status_detail

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

8. Đồng bộ cấu hình Tình Trạng Học Tập sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_th_student_status

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

9. Đồng bộ thông tin Quận/Huyện sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_country_district

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

10. Đồng bộ thông tin Phường/Xã sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_country_ward

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

11. Đồng bộ thông tin Tỉnh/Thành phố sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_country_state

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

12. Đồng bộ thông tin cơ hội CRM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * map_data_from_sync
    * get_data_sync
    * th_trigger_crm_lead
    * th_trigger_crm_lead_daily_sync

4. Chức năng
    * Map ID bản ghi của hệ thống hiện tại với ID bản ghi của hệ thống bên ngoài
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

13. Đồng bộ cấu hình Mối quan hệ CRM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_crm_stage

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

14. Đồng bộ cấu hình Thẻ CRM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_crm_tag

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

15. Đồng bộ thông tin log note sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_mail_message

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

16. Đồng bộ thông tin Liên hệ sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * map_data_res_partner_from_sync
    * get_contact_data_sync
    * th_trigger_update_contact

4. Chức năng
    * Map ID bản ghi của hệ thống hiện tại với ID bản ghi của hệ thống bên ngoài
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

17. Đồng bộ thông tin Danh xưng liên hệ sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_create_or_update_res_partner_title

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

18. Đồng bộ thông tin Hồ sơ sinh viên sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_student_profile

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

19. Đồng bộ thông tin cơ hội APM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * map_data_apm_from_sync
    * get_data_sync
    * th_trigger_apm_lead

4. Chức năng
    * Map ID bản ghi của hệ thống hiện tại với ID bản ghi của hệ thống bên ngoài
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

20. Đồng bộ cấu hình Chiến dịch APM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_apm_campaign

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

21. Đồng bộ cấu hình Mối quan hệ APM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_apm_level

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

22. Đồng bộ thông tin Đơn hàng sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_sale_order

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.

23. Đồng bộ cấu hình Đội chăm sóc APM sang bên B2B
--------------------------------------
1. Phương thức
    * Post
    * Put
    * Delete

2. URL
    * {th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}

3. Hàm
    * get_data_sync
    * th_trigger_apm_team

4. Chức năng
    * Lấy data để đồng bộ.
    * Tự động trigger đồng bộ khi có sự thay đổi.
