<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="th_sync_performance_log_view_tree" model="ir.ui.view">
        <field name="name">th.sync.performance.log.tree</field>
        <field name="model">th.sync.performance.log</field>
        <field name="arch" type="xml">
            <tree string="Log hiệu suất đồng bộ" default_order="create_date desc" decoration-success="th_status == 'success'" decoration-danger="th_status == 'error'" decoration-warning="th_is_slow == True" decoration-muted="th_is_very_slow == True">
                <field name="create_date" string="Thời gian"/>
                <field name="th_model_name" string="Model"/>
                <field name="th_endpoint" string="Endpoint"/>
                <field name="th_method" string="Method"/>
                <field name="th_system_source" string="Từ"/>
                <field name="th_system_target" string="Đến"/>
                <field name="th_record_id" string="Record ID"/>
                <field name="th_prepare_time" string="<PERSON><PERSON><PERSON> bị (ms)" optional="hide"/>
                <field name="th_request_time" string="HTTP (ms)"/>
                <field name="th_processing_time" string="Xử lý (ms)" optional="hide"/>
                <field name="th_total_time" string="Tổng (ms)"/>
                <field name="th_performance_level" string="Hiệu suất"/>
                <field name="th_status" string="Trạng thái"/>
                <field name="th_response_size" string="Size (bytes)" optional="hide"/>
                <field name="th_is_slow" invisible="1"/>
                <field name="th_is_very_slow" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="th_sync_performance_log_view_form" model="ir.ui.view">
        <field name="name">th.sync.performance.log.form</field>
        <field name="model">th.sync.performance.log</field>
        <field name="arch" type="xml">
            <form string="Chi tiết log hiệu suất">
                <header>
                    <field name="th_status" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="th_endpoint" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="th_model_name" readonly="1"/>
                                - 
                            <field name="th_method" readonly="1"/>
                        </h2>
                    </div>

                    <group>
                        <group string="Thông tin cơ bản">
                            <field name="create_date" string="Thời gian đồng bộ"/>
                            <field name="th_system_source"/>
                            <field name="th_system_target"/>
                            <field name="th_record_id"/>
                            <field name="th_performance_level"/>
                        </group>
                        <group string="Timing Metrics (ms)">
                            <field name="th_prepare_time"/>
                            <field name="th_request_time"/>
                            <field name="th_processing_time"/>
                            <field name="th_total_time"/>
                            <field name="th_response_size"/>
                        </group>
                    </group>

                    <group string="Performance Flags">
                        <field name="th_is_slow"/>
                        <field name="th_is_very_slow"/>
                    </group>

                    <notebook>
                        <page string="URL và Mô tả">
                            <group>
                                <field name="th_url" widget="url"/>
                                <field name="th_description" widget="text"/>
                            </group>
                        </page>
                        <page string="Chi tiết lỗi" attrs="{'invisible': [('th_status', '!=', 'error')]}">
                            <field name="th_error_details" widget="text"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="th_sync_performance_log_view_search" model="ir.ui.view">
        <field name="name">th.sync.performance.log.search</field>
        <field name="model">th.sync.performance.log</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm log hiệu suất">
                <field name="th_model_name"/>
                <field name="th_endpoint"/>
                <field name="th_system_source"/>
                <field name="th_system_target"/>
                <field name="th_status"/>

                <filter string="Thành công" name="success" domain="[('th_status', '=', 'success')]"/>
                <filter string="Lỗi" name="error" domain="[('th_status', '=', 'error')]"/>
                <filter string="Chậm" name="slow" domain="[('th_is_slow', '=', True)]"/>
                <filter string="Rất chậm" name="very_slow" domain="[('th_is_very_slow', '=', True)]"/>

                <separator/>
                <filter string="Hôm nay" name="today" domain="[('th_sync_date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="Tuần này" name="this_week" domain="[('th_sync_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="Tháng này" name="this_month" domain="[('th_sync_date', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>

                <group expand="0" string="Nhóm theo">
                    <filter string="Model" name="group_model" context="{'group_by': 'th_model_name'}"/>
                    <filter string="Endpoint" name="group_endpoint" context="{'group_by': 'th_endpoint'}"/>
                    <filter string="Trạng thái" name="group_status" context="{'group_by': 'th_status'}"/>
                    <filter string="Hiệu suất" name="group_performance" context="{'group_by': 'th_performance_level'}"/>
                    <filter string="Ngày" name="group_date" context="{'group_by': 'th_sync_date'}"/>
                    <filter string="Giờ" name="group_hour" context="{'group_by': 'th_sync_hour'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Pivot View -->
    <record id="th_sync_performance_log_view_pivot" model="ir.ui.view">
        <field name="name">th.sync.performance.log.pivot</field>
        <field name="model">th.sync.performance.log</field>
        <field name="arch" type="xml">
            <pivot string="Phân tích hiệu suất đồng bộ">
                <field name="th_sync_date" type="row"/>
                <field name="th_model_name" type="col"/>
                <field name="th_total_time" type="measure"/>
                <field name="th_request_time" type="measure"/>
                <field name="th_processing_time" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Graph View -->
    <record id="th_sync_performance_log_view_graph" model="ir.ui.view">
        <field name="name">th.sync.performance.log.graph</field>
        <field name="model">th.sync.performance.log</field>
        <field name="arch" type="xml">
            <graph string="Biểu đồ hiệu suất đồng bộ" type="line">
                <field name="th_sync_date"/>
                <field name="th_total_time" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Action -->
    <record id="th_sync_performance_log_action" model="ir.actions.act_window">
        <field name="name">Log hiệu suất đồng bộ</field>
        <field name="res_model">th.sync.performance.log</field>
        <field name="view_mode">tree,form,pivot,graph</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có log hiệu suất đồng bộ nào!
            </p>
            <p>
                Log hiệu suất sẽ được tạo tự động khi có hoạt động đồng bộ dữ liệu giữa các hệ thống.
            </p>
        </field>
    </record>


    <!-- Menu Items -->
    <menuitem id="th_sync_performance_log_menu" name="Log hiệu suất" parent="th_fast_api_view_menu" action="th_sync_performance_log_action" sequence="10"/>
</odoo>
