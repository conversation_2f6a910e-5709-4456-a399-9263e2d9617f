<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<!--        data university-->
        <record id="th_aum_university_origin" model="th.origin">
            <field name="name"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (AUM)</field>
            <field name="th_code">AUM</field>
        </record>
        <record id="th_origin_vmc" model="th.origin">
            <field name="name">VMC</field>
            <field name="th_code">AUM.VMC</field>
            <field name="active">False</field>
        </record>
        <record id="th_origin_vstep" model="th.origin">
            <field name="name">VSTEP</field>
            <field name="th_code">AUM.VSTEP</field>
            <field name="active">False</field>
        </record>

<!--        data ownership unit-->
        <record id="th_aum_ownership_unit" model="th.ownership.unit">
            <field name="name">AUM</field>
            <field name="th_description">AUM</field>
            <field name="th_code">aum</field>
        </record>

<!--        data channel-->
        <record id="th_aum_info_channel_web" model="th.info.channel">
            <field name="name">Web</field>
            <field name="th_description">Web form</field>
        </record>
        <record id="th_aum_info_channel_chat_web" model="th.info.channel">
            <field name="name">Chat Web</field>
            <field name="th_description">Chat Web</field>
        </record>

        <record id="th_aum_info_channel_loc" model="th.info.channel">
            <field name="name">Lọc</field>
            <field name="th_description">Lọc</field>
        </record>
<!--        data module-->
        <record id="th_apm_module" model="therp.module">
            <field name="name">APM</field>
            <field name="active">False</field>
        </record>
        <record id="th_prm_module" model="therp.module">
            <field name="name">PRM</field>
            <field name="active">False</field>
        </record>
        <record id="th_trm_module" model="therp.module">
            <field name="name">TRM</field>
            <field name="active">False</field>
        </record>
        <record id="th_crm_module" model="therp.module">
            <field name="name">CRM</field>
            <field name="active">False</field>
        </record>
        <record id="th_srm_module" model="therp.module">
            <field name="name">SRM</field>
            <field name="active">False</field>
        </record>

        <record id="th_channel_unknown" model="th.info.channel">
            <field name="name">Không xác định</field>
        </record>

    </data>
    <record id="th_setup_seq_customer_code" model="ir.sequence">
        <field name="name">Customer Code</field>
        <field name="code">customer.code</field>
        <field name="prefix">A</field>
        <field name="padding">9</field>
        <field name="company_id" eval="False"></field>
    </record>
    <record id="th_setup_seq_affiliate_code" model="ir.sequence">
        <field name="name">Affiliate Code</field>
        <field name="code">affiliate.code</field>
        <field name="prefix">REF</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"></field>
    </record>

</odoo>