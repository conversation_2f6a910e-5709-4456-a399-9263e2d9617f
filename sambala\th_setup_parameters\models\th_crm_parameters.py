import xmlrpc

from odoo import fields, models, api, _
import json
from odoo.exceptions import ValidationError, UserError
import requests

# def _get_server_aff(self):
#     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
#                                                   order='id desc')
#     if not server_api:
#         raise ValidationError('Không tìm thấy server!')
#     try:
#         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
#         return server_api, result_apis
#     except Exception as e:
#         print(e)
#         return False, False


class ThOrigin(models.Model):
    _name = "th.origin"
    _description = "Xuất xứ"

    name = fields.Char(string="Xuất xứ", required=True)
    th_code = fields.Char(string="<PERSON>ã xuất xứ", required=True)
    th_university_major_ids = fields.One2many(comodel_name="th.university.major", inverse_name="th_origin_id", string="Ngành học")
    th_description = fields.Text(string="<PERSON>ô tả")
    th_address = fields.Text(string="Địa chỉ")
    color = fields.Integer("Color Index", default=0)
    th_partner_id = fields.One2many(comodel_name="res.partner", inverse_name="th_origin_id", string="Liên hệ")
    th_api_state = fields.Boolean(default=True)
    active = fields.Boolean(default=True)
    th_module_ids = fields.Many2many(comodel_name="therp.module", string="Module")
    aff_warehouse_id = fields.Integer(string="Aff warehouse id", copy=False)
    th_origin_b2b_id = fields.Integer("Id Xuất xứ B2B", copy=False)
    th_program_management_ids = fields.Many2many('res.users', string='Quản lý chương trình')
    th_program_management_crm_ids = fields.Many2many('res.users', string='Quản lý chương trình CRM', relation="th_program_management_origin_crm_rel")
    th_mkt_user_ids = fields.Many2many('res.users', string='Quản lý chương trình CRM của MKT', relation="th_mkt_user_crm_rel")
    th_profile_crm_ids = fields.Many2many('res.users', string='Quản lý hồ sơ CRM', relation="th_profile_origin_crm_rel")
    is_vstep = fields.Boolean(string='Thuộc vstep')

    @api.model_create_multi
    def create(self, values_list):
        res = super(ThOrigin, self).create(values_list)
        if not self._context.get('th_test_import', False):
            for rec in res:
                self.update_th_warehouse(rec, state='create')
                # self.th_action_sync_b2b_origin(rec, state='create')
        return res

    def write(self, values):
        res = super(ThOrigin, self).write(values)
        if not self._context.get('th_test_import', False) and values.get('aff_warehouse_id', '') == '' and any(values.get(f'{field}', False) for field in ['name', 'th_code', 'th_description', 'th_module_ids',]):
            self.update_th_warehouse(self, state='write')
        # if (not self._context.get('th_test_import', False) and (values.get('aff_warehouse_id', '') == '' and any(
        #         values.get(f'{field}', False) for field in ['name', 'th_code', 'th_description', 'th_module_ids', ]))):
        #     for rec in self:
        #         rec.th_action_sync_b2b_origin(rec, state='write')
        return res

    def unlink(self):
        for rec in self:
            self.update_th_warehouse(rec, state='unlink')
            if not rec.th_api_state:
                rec.write({'active': False})
                return False
        return super().unlink()

    def update_th_warehouse(self, res=None, state=None):
        val = {}
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password

            if not res:
                res = self.search([])

            for rec in res:
                val = {
                    'name': rec.name,
                    'th_code': rec.th_code,
                    'th_description': rec.th_description,
                }

                if state in ['create', 'write'] and not rec.aff_warehouse_id:
                    warehouse_id = result_apis.execute_kw(db, uid_api, password, 'th.warehouse', 'create', [val],
                                                          {'context': {'module': rec.th_module_ids.mapped('name')}})
                    rec.write({'aff_warehouse_id': warehouse_id})
                if state == 'write' and rec.aff_warehouse_id:
                    result_apis.execute_kw(db, uid_api, password, 'th.warehouse', 'write',
                                           [int(rec.aff_warehouse_id), val],
                                           {'context': {'module': rec.th_module_ids.mapped('name')}})
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(res.ids),
                'th_input_data': str(val),
                'th_function_call': str('update_th_warehouse'),
            })
        return True

    @api.constrains('th_code')
    def _constraint_th_code_origin(self):
        if any(self.search([('id', '!=', rec.id), ('th_code', '=', rec.th_code)]) for rec in self):
            raise ValidationError(_("Mã xuất xứ đã tồn tại. Vui lòng kiểm tra lại!"))

    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_origin(rec)
    #     res = super(ThOrigin, self).unlink()
    #     return res

    # def th_action_sync_b2b_origin(self, res, state=None):
    #     val_warehouse = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             th_university_major_ids = [(5, 0, 0)] + [(0, 0, {
    #                                            'th_major_id': line.th_major_id.th_major_b2b_id if line.th_major_id.th_major_b2b_id else False,
    #                                            'th_major_code_university': line.th_major_code_university,
    #                                        }) for line in rec.th_university_major_ids if line.th_major_id.th_major_b2b_id ]
    #             vals = {'name': rec.name,
    #                     'th_code': rec.th_code,
    #                     'th_address': rec.th_address,
    #                     'color': rec.color,
    #                     'th_description': rec.th_description,
    #                     'th_module_ids': [(6, 0, rec.th_module_ids.mapped('th_module_b2b_id') if rec.th_module_ids.mapped('th_module_b2b_id')!=[0] else [])],
    #                     'th_university_major_ids': th_university_major_ids,
    #                     'th_origin_samp_id': rec.id}
    #
    #             val_warehouse = {
    #                 'name': rec.name,
    #                 'th_code': rec.th_code,
    #                 'th_description': rec.th_description,
    #             }
    #             if state in ['create', 'write'] and not rec.aff_warehouse_id:
    #                 warehouse_id = result_apis.execute_kw(db, uid_api, password, 'th.warehouse', 'create', [val_warehouse],
    #                                                       {'context': {'module': rec.th_module_ids.mapped('name')}})
    #                 rec.with_context(th_test_import=True).write({'aff_warehouse_id': warehouse_id})
    #             if state == 'write' and rec.aff_warehouse_id:
    #                 result_apis.execute_kw(db, uid_api, password, 'th.warehouse', 'write',
    #                                        [int(rec.aff_warehouse_id), val_warehouse],
    #                                        {'context': {'module': rec.th_module_ids.mapped('name')}})
    #             if not rec.th_origin_b2b_id and not self._context.get('is_delete'):
    #                 origin = result_apis.execute_kw(db, uid_api, password, 'th.origin', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_origin_b2b_id': origin})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     origin = result_apis.execute_kw(db, uid_api, password, 'th.origin', 'write',
    #                                                     [[rec.th_origin_b2b_id], vals])
    #                 else:
    #                     origin = result_apis.execute_kw(db, uid_api, password, 'th.origin', 'unlink',
    #                                                     [[rec.th_origin_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(val_warehouse),
    #             'th_function_call': str('th_action_sync_b2b_origin'),
    #         })
    #         return


class ThMajor(models.Model):
    _name = "th.major"
    _description = "Ngành học"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Tên ngành", required=True)
    # th_university_id = fields.Many2one(comodel_name="th.university", string="Trường đại học")
    th_description = fields.Text(string="Mô tả")
    th_major_code_aum = fields.Char(string="Mã ngành AUM")
    th_major_b2b_id = fields.Integer("Id Ngành học B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThMajor, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_major(rec)
    #     return res

    # def write(self, values):
    #     res = super(ThMajor, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_major(rec)
    #     return res

    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_major(rec)
    #     res = super(ThMajor, self).unlink()
    #     return res

    # def th_action_sync_b2b_major(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_major_code_aum': rec.th_major_code_aum,
    #                     'th_major_samp_id': rec.id}
    #             if not rec.th_major_b2b_id and not self._context.get('is_delete'):
    #                 major = result_apis.execute_kw(db, uid_api, password, 'th.major', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_major_b2b_id': major})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     major = result_apis.execute_kw(db, uid_api, password, 'th.major', 'write',
    #                                                     [[rec.th_major_b2b_id], vals])
    #                 else:
    #                     major = result_apis.execute_kw(db, uid_api, password, 'th.major', 'unlink',
    #                                                     [[rec.th_major_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_major'),
    #         })
    #         return


class ThUniversityMajor(models.Model):
    _name = "th.university.major"
    _description = "Trường - ngành"
    _rec_name = "th_major_id"

    active = fields.Boolean('Active', default=True)
    th_major_id = fields.Many2one(comodel_name="th.major", string="Ngành học")
    th_major_code_university = fields.Char(string="Mã ngành", )
    # th_university_id = fields.Many2one(comodel_name="th.university", string="Trường đại học")
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường đại học")

    @api.constrains('th_major_id', 'th_origin_id')
    def _constraint_th_code_origin(self):
        if any(self.search([('id', '!=', rec.id), ('th_major_id', '=', rec.th_major_id.id), ('th_origin_id', '=', rec.th_origin_id.id)]) for rec in self):
            raise ValidationError(_("Ngành trên đã được thiết lập. Vui lòng kiểm tra lại!"))


class ThStatusDetail(models.Model):
    _name = "th.status.detail"
    _description = "Trạng thái chi tiết"

    name = fields.Char(string="Tên trạng thái", required=True)
    sequence = fields.Integer('Trình tự')
    th_description = fields.Text(string="Mô tả")
    th_status_category_id = fields.Many2one(comodel_name="th.status.category", string="Nhóm trạng thái", ondelete="cascade")
    th_type = fields.Selection(string="Loại",
                               selection=[('crm', 'CRM'), ('srm', 'SRM'), ('prm', 'PRM'), ('tom', 'TOM'), ('trm', 'TRM'), ('apm', 'APM'), ('ccs', 'CSKH')],
                               related="th_status_category_id.th_type", store=True, readonly=False)
    th_s_c_d_b2b_id = fields.Integer("Id Trạng thái chi tiết B2B", copy=False)


class ThStatusCategory(models.Model):
    _name = "th.status.category"
    _description = "Danh sách trạng thái"
    # _inherit = ['mail.thread', 'mail.activity.mixin']

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Nhóm trạng thái", required=True)
    sequence = fields.Integer('Trình tự')
    th_description = fields.Text(string="Mô tả")
    th_type = fields.Selection(string="Loại",
                               selection=[('crm', 'CRM'), ('srm', 'SRM'), ('prm', 'PRM'), ('tom', 'TOM'), ('trm', 'TRM'), ('apm', 'APM'), ('ccs', 'CSKH')])
    th_status_detail_ids = fields.One2many(comodel_name="th.status.detail", inverse_name="th_status_category_id",string="Trang thái chi tiết")
    th_s_c_b2b_id = fields.Integer("Id Danh sách trạng thái B2B", copy=False)

    @api.constrains('name')
    def _check_name(self):
        for rec in self:
            if self.search_count([('name', '=', rec.name), ('id', '!=', rec.id), ('th_type', '=', rec.th_type)]) > 0:
                raise ValidationError("Nhóm trạng thái %s đã tồn tại!" % rec.name)


class ThOwnershipUnit(models.Model):
    _name = "th.ownership.unit"
    _description = "Đơn vị sở hữu"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Tên sở hữu", required=True)
    th_description = fields.Text(string="Mô tả")
    th_partner_id = fields.Many2one(comodel_name="res.partner", string="Tên liên hệ")
    color = fields.Integer("Color Index", default=0)
    th_code = fields.Char("Mã đơn vị sở hữu", required=1)
    th_own_b2b_id = fields.Integer("Id Đơn vị sở hữu B2B", copy=False)
    th_is_sync = fields.Boolean("Bật đồng bộ hóa", default=False)
    th_type = fields.Selection(string="Đơn vị", selection=[('aum', 'AUM'), ('other', 'Đối tác')], default='aum', required=1)

    # @api.model
    # def create(self, values):
    #     res = super(ThOwnershipUnit, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_owner(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThOwnershipUnit, self).write(values)
    #     if not self._context.get('th_test_import', False) and not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_owner(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_owner(rec)
    #     res = super(ThOwnershipUnit, self).unlink()
    #     return res

    # def th_action_sync_b2b_owner(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_code': rec.th_code,
    #                     'color': rec.color,
    #                     'th_own_samp_id': rec.id}
    #             if not rec.th_own_b2b_id and not self._context.get('is_delete'):
    #                 owner = result_apis.execute_kw(db, uid_api, password, 'th.ownership.unit', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_own_b2b_id': owner})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     owner = result_apis.execute_kw(db, uid_api, password, 'th.ownership.unit', 'write',
    #                                                     [[rec.th_own_b2b_id], vals])
    #                 else:
    #                     owner = result_apis.execute_kw(db, uid_api, password, 'th.ownership.unit', 'unlink',
    #                                                     [[rec.th_own_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_owner'),
    #         })
    #         return


class ThInfoChannel(models.Model):
    _name = "th.info.channel"
    _description = "Channel"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Kênh", required=True)
    th_description = fields.Text(string="Mô tả")
    th_channel_b2b_id = fields.Integer("Id Channel B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThInfoChannel, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_channel(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThInfoChannel, self).write(values)
    #     if not self._context.get('th_test_import', False) and not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_channel(rec)
    #     return res

    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_channel(rec)
    #     res = super(ThInfoChannel, self).unlink()
    #     return res

    # def th_action_sync_b2b_channel(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_channel_samp_id': rec.id}
    #             if not rec.th_channel_b2b_id and not self._context.get('is_delete'):
    #                 channel = result_apis.execute_kw(db, uid_api, password, 'th.info.channel', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_channel_b2b_id': channel})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     channel = result_apis.execute_kw(db, uid_api, password, 'th.info.channel', 'write',
    #                                                     [[rec.th_channel_b2b_id], vals])
    #                 else:
    #                     channel = result_apis.execute_kw(db, uid_api, password, 'th.info.channel', 'unlink',
    #                                                     [[rec.th_channel_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_channel'),
    #         })
    #         return


class THSourceGroup(models.Model):
    _name = "th.source.group"
    _description = "Nhóm nguồn"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Nhóm nguồn", required=True)
    th_description = fields.Text(string="Mô tả")
    th_source_b2b_id = fields.Integer("Id Nhóm nguồn B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(THSourceGroup, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_source(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(THSourceGroup, self).write(values)
    #     if not self._context.get('th_test_import', False) and not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_source(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_source(rec)
    #     res = super(THSourceGroup, self).unlink()
    #     return res

    # def th_action_sync_b2b_source(self, res):
    #     return
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_source_samp_id': rec.id}
    #             if not rec.th_source_b2b_id and not self._context.get('is_delete'):
    #                 source = result_apis.execute_kw(db, uid_api, password, 'th.source.group', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_source_b2b_id': source})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     source = result_apis.execute_kw(db, uid_api, password, 'th.source.group', 'write',
    #                                                     [[rec.th_source_b2b_id], vals])
    #                 else:
    #                     source = result_apis.execute_kw(db, uid_api, password, 'th.source.group', 'unlink',
    #                                                     [[rec.th_source_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_source'),
    #         })
    #         return


class ThAdmissionsStation(models.Model):
    _name = "th.admissions.station"
    _description = "Trạm tuyển sinh"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Mã Trạm", required=True)
    th_name = fields.Char(string="Tên Trạm")
    th_description = fields.Text(string="Mô tả")
    th_station_b2b_id = fields.Integer("Id Trạm tuyển sinh B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThAdmissionsStation, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_station(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThAdmissionsStation, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_station(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_station(rec)
    #     res = super(ThAdmissionsStation, self).unlink()
    #     return res

    # def th_action_sync_b2b_station(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_station_samp_id': rec.id}
    #             if not rec.th_station_b2b_id and not self._context.get('is_delete'):
    #                 station = result_apis.execute_kw(db, uid_api, password, 'th.admissions.station', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_station_b2b_id': station})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     station = result_apis.execute_kw(db, uid_api, password, 'th.admissions.station', 'write',
    #                                                     [[rec.th_station_b2b_id], vals])
    #                 else:
    #                     station = result_apis.execute_kw(db, uid_api, password, 'th.admissions.station', 'unlink',
    #                                                     [[rec.th_station_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_station'),
    #         })
    #         return


class ThAdmissionsRegion(models.Model):
    _name = "th.admissions.region"
    _description = "Vùng tuyển sinh"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Vùng tuyển sinh", required=True)
    th_description = fields.Text(string="Mô tả")
    th_region_b2b_id = fields.Integer("Id Vùng tuyển sinh B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThAdmissionsRegion, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_region(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThAdmissionsRegion, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_region(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_region(rec)
    #     res = super(ThAdmissionsRegion, self).unlink()
    #     return res

    # def th_action_sync_b2b_region(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_region_samp_id': rec.id}
    #             if not rec.th_region_b2b_id and not self._context.get('is_delete'):
    #                 region = result_apis.execute_kw(db, uid_api, password, 'th.admissions.region', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_region_b2b_id': region})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     region = result_apis.execute_kw(db, uid_api, password, 'th.admissions.region', 'write', [[rec.th_region_b2b_id], vals])
    #                 else:
    #                     region = result_apis.execute_kw(db, uid_api, password, 'th.admissions.region', 'unlink', [[rec.th_region_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_region'),
    #         })
    #         return


class ThGraduationSystem(models.Model):
    _name = "th.graduation.system"
    _description = "Hệ tốt nghiệp"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Mã hệ tốt nghiệp", required=True)
    th_description = fields.Char(string="Hệ tốt nghiệp", )
    th_graduation_b2b_id = fields.Integer("Id hệ tốt nghiệp B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThGraduationSystem, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_graduation(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThGraduationSystem, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_graduation(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_graduation(rec)
    #     res = super(ThGraduationSystem, self).unlink()
    #     return res

    # def th_action_sync_b2b_graduation(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     try:
    #         server_api, result_apis = _get_server_aff(self)
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_description': rec.th_description,
    #                     'th_graduation_samp_id': rec.id}
    #             if not rec.th_graduation_b2b_id and not self._context.get('is_delete'):
    #                 graduation = result_apis.execute_kw(db, uid_api, password, 'th.graduation.system', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_graduation_b2b_id': graduation})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     graduation = result_apis.execute_kw(db, uid_api, password, 'th.graduation.system', 'write',
    #                                                     [[rec.th_graduation_b2b_id], vals])
    #                 else:
    #                     graduation = result_apis.execute_kw(db, uid_api, password, 'th.graduation.system', 'unlink',
    #                                                     [[rec.th_graduation_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_graduation'),
    #         })
    #         return


class ThCombinationOfSubjects(models.Model):
    _name = "th.combination.subjects"
    _description = "Tổ hợp môn"
    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Tổ hợp môn", required=True)
    th_description = fields.Char(string="Mô tả", )
