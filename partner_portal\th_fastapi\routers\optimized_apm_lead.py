"""
Optimized APM Lead Creation Algorithm
C<PERSON><PERSON> thuật toán tối ưu cho việc tạo APM Lead
"""

from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


@dataclass
class ProcessResult:
    """Kết quả xử lý"""
    success: bool
    data: Any
    error: Optional[str] = None
    execution_time: float = 0


class APMLeadOptimizer:
    """Class chính để tối ưu hóa việc tạo APM Lead"""
    
    def __init__(self, fastapi, config: Optional[Dict] = None):
        self.fastapi = fastapi
        self.config = config or {
            'batch_size': 100,
            'max_workers': 5,
            'max_retries': 3,
            'cache_enabled': True
        }
        
        # Cache
        self._country_cache = None
        self._partner_cache = {}
        
    @property
    def country_cache(self) -> Dict[str, int]:
        """Lazy loading country cache"""
        if self._country_cache is None and self.config['cache_enabled']:
            countries = self.fastapi.env['res.country'].search([])
            self._country_cache = {country.code: country.id for country in countries}
            logger.info(f"Loaded {len(self._country_cache)} countries into cache")
        return self._country_cache or {}
    
    def validate_records(self, records: List) -> Tuple[List, List]:
        """
        Algorithm 1: Early Validation
        Validate tất cả records trước khi xử lý
        """
        valid_records = []
        invalid_records = []
        
        for i, record in enumerate(records):
            try:
                # Business logic validation
                if record.id_b2b == 0:
                    if not record.th_data_apm:
                        invalid_records.append({
                            'index': i,
                            'record': record,
                            'error': 'Missing th_data_apm for new record'
                        })
                        continue
                    
                    # Validate required fields for new records
                    values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                    if not values:
                        invalid_records.append({
                            'index': i,
                            'record': record,
                            'error': 'Empty th_data_apm'
                        })
                        continue
                
                valid_records.append(record)
                
            except Exception as e:
                invalid_records.append({
                    'index': i,
                    'record': record,
                    'error': f'Validation error: {str(e)}'
                })
        
        logger.info(f"Validation: {len(valid_records)} valid, {len(invalid_records)} invalid")
        return valid_records, invalid_records
    
    def group_records_by_operation(self, records: List) -> Dict[str, List]:
        """
        Algorithm 2: Operation Grouping
        Nhóm records theo loại operation để batch processing
        """
        groups = {
            'create': [],
            'update': [],
            'delete': []
        }
        
        for record in records:
            if record.id_b2b == 0:
                groups['create'].append(record)
            elif record.id_b2b != 0 and record.th_data_apm:
                groups['update'].append(record)
            elif record.id_b2b != 0 and not record.th_data_apm:
                groups['delete'].append(record)
        
        logger.info(f"Grouped: {len(groups['create'])} create, "
                   f"{len(groups['update'])} update, {len(groups['delete'])} delete")
        return groups
    
    def optimize_partner_creation(self, records_to_create: List) -> Dict[str, Any]:
        """
        Algorithm 3: Partner Creation Optimization
        Tối ưu việc tạo partner bằng cách:
        1. Bulk query existing partners
        2. Batch create new partners
        3. Use country cache
        """
        partners_data = []
        partner_mappings = {}
        
        # Extract all partner_info from records
        for i, record in enumerate(records_to_create):
            values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
            partner_info = values.get('partner_info')
            
            if partner_info and not values.get('th_partner_id'):
                # Use country cache
                if partner_info.get('country_id') and partner_info['country_id'] in self.country_cache:
                    country_id = self.country_cache[partner_info['country_id']]
                    partner_info['country_id'] = country_id
                    partner_info['th_country_id'] = country_id
                
                partners_data.append((i, partner_info))
        
        # Bulk create partners if any
        if partners_data:
            try:
                partner_infos = [data[1] for data in partners_data]
                created_partners = self.fastapi.env['res.partner'].create(partner_infos)
                
                # Map created partners back to records
                for (record_index, _), partner in zip(partners_data, created_partners):
                    partner_mappings[record_index] = partner.id
                    
                logger.info(f"Bulk created {len(created_partners)} partners")
                
            except Exception as e:
                logger.error(f"Bulk partner creation failed: {str(e)}")
                # Fallback to individual creation
                for record_index, partner_info in partners_data:
                    try:
                        partner = self.fastapi.env['res.partner'].create(partner_info)
                        partner_mappings[record_index] = partner.id
                    except Exception as partner_e:
                        logger.error(f"Individual partner creation failed: {str(partner_e)}")
        
        return partner_mappings
    
    def batch_process_creates(self, create_records: List) -> List[ProcessResult]:
        """
        Algorithm 4: Batch Create Processing
        Xử lý batch creation với optimization
        """
        start_time = time.time()
        results = []
        
        # Optimize partner creation first
        partner_mappings = self.optimize_partner_creation(create_records)
        
        # Process each record with optimized partner data
        for i, record in enumerate(create_records):
            record_start = time.time()
            
            try:
                values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                partner_info = values.pop('partner_info', None)
                
                # Use pre-created partner if available
                if i in partner_mappings:
                    values['th_partner_id'] = partner_mappings[i]
                
                # Create APM lead
                apm_lead = self.fastapi.env['th.apm'].sudo().with_context(
                    th_sync=True
                ).th_create_th_apm(values)
                
                execution_time = time.time() - record_start
                results.append(ProcessResult(
                    success=True,
                    data={
                        'id': apm_lead.id,
                        'th_partner_id': partner_mappings.get(i, False)
                    },
                    execution_time=execution_time
                ))
                
            except Exception as e:
                execution_time = time.time() - record_start
                results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=str(e),
                    execution_time=execution_time
                ))
                logger.error(f"Create APM lead failed for record {i}: {str(e)}")
        
        total_time = time.time() - start_time
        logger.info(f"Batch create processed {len(create_records)} records in {total_time:.2f}s")
        
        return results
    
    def batch_process_updates(self, update_records: List) -> List[ProcessResult]:
        """
        Algorithm: Batch Update Processing
        Xử lý batch updates với optimization
        """
        start_time = time.time()
        results = []
        
        # Group updates by ID for potential bulk operations
        update_mapping = {}
        for record in update_records:
            update_mapping[record.id_b2b] = record.th_data_apm
        
        # Bulk load all records to update
        ids_to_update = list(update_mapping.keys())
        try:
            apm_records = self.fastapi.env['th.apm'].browse(ids_to_update)
            existing_ids = {rec.id for rec in apm_records if rec.exists()}
            
            # Process existing records
            for record in update_records:
                record_start = time.time()
                
                try:
                    if record.id_b2b not in existing_ids:
                        raise Exception(f"APM record {record.id_b2b} not found")
                    
                    apm_record = apm_records.filtered(lambda r: r.id == record.id_b2b)
                    data_to_update = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                    
                    apm_record.sudo().with_context(th_sync=True).th_write_th_apm(data_to_update)
                    
                    execution_time = time.time() - record_start
                    results.append(ProcessResult(
                        success=True,
                        data={'id': record.id_b2b},
                        execution_time=execution_time
                    ))
                    
                except Exception as e:
                    execution_time = time.time() - record_start
                    results.append(ProcessResult(
                        success=False,
                        data=None,
                        error=str(e),
                        execution_time=execution_time
                    ))
                    logger.error(f"Update APM lead failed for record {record.id_b2b}: {str(e)}")
            
        except Exception as e:
            logger.error(f"Batch update preparation failed: {str(e)}")
            # Fallback to individual processing
            for record in update_records:
                results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=f"Batch update failed: {str(e)}"
                ))
        
        total_time = time.time() - start_time
        logger.info(f"Batch update processed {len(update_records)} records in {total_time:.2f}s")
        
        return results
    
    def batch_process_deletes(self, delete_records: List) -> List[ProcessResult]:
        """
        Algorithm: Batch Delete Processing with Business Logic Validation
        Xử lý batch deletes với validation
        """
        start_time = time.time()
        results = []
        
        # Extract IDs to delete
        ids_to_delete = [record.id_b2b for record in delete_records]
        
        try:
            # Bulk load records with business logic validation
            apm_records = self.fastapi.env['th.apm'].browse(ids_to_delete)
            existing_records = apm_records.filtered(lambda r: r.exists())
            
            # Validate business rules before deletion
            invalid_deletes = []
            valid_deletes = []
            
            for apm_record in existing_records:
                if apm_record.th_partner_id and apm_record.th_type_of_care == 'advise':
                    invalid_deletes.append({
                        'id': apm_record.id,
                        'error': f"Cannot delete APM {apm_record.id} - already assigned to advisor"
                    })
                else:
                    valid_deletes.append(apm_record.id)
            
            # Add results for invalid deletes
            for invalid in invalid_deletes:
                results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=invalid['error']
                ))
            
            # Batch delete valid records
            if valid_deletes:
                valid_records = existing_records.filtered(lambda r: r.id in valid_deletes)
                valid_records.sudo().with_context(th_sync=True).unlink()
                
                # Add success results
                for record_id in valid_deletes:
                    results.append(ProcessResult(
                        success=True,
                        data={'id': record_id, 'message': 'Successfully deleted'}
                    ))
            
            # Handle non-existing records
            existing_ids = {rec.id for rec in existing_records}
            for record_id in ids_to_delete:
                if record_id not in existing_ids:
                    results.append(ProcessResult(
                        success=False,
                        data=None,
                        error=f"APM record {record_id} not found"
                    ))
            
        except Exception as e:
            logger.error(f"Batch delete failed: {str(e)}")
            # Fallback - mark all as failed
            for record in delete_records:
                results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=f"Batch delete failed: {str(e)}"
                ))
        
        total_time = time.time() - start_time
        logger.info(f"Batch delete processed {len(delete_records)} records in {total_time:.2f}s")
        
        return results
    
    def process_with_savepoints(self, operation_func, data_batch: List) -> List[ProcessResult]:
        """
        Algorithm 5: Transaction Management
        Xử lý với savepoint để isolation
        """
        results = []
        
        for item in data_batch:
            try:
                with self.fastapi.env.cr.savepoint():
                    result = operation_func(item)
                    results.append(ProcessResult(
                        success=True,
                        data=result
                    ))
            except Exception as e:
                results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=str(e)
                ))
                logger.error(f"Transaction failed: {str(e)}")
        
        return results
    
    def process_in_chunks(self, records: List, chunk_size: Optional[int] = None) -> List[ProcessResult]:
        """
        Algorithm 6: Memory Optimization
        Xử lý theo chunks để tránh memory overflow
        """
        chunk_size = chunk_size or self.config['batch_size']
        all_results = []
        
        for i in range(0, len(records), chunk_size):
            chunk = records[i:i + chunk_size]
            logger.info(f"Processing chunk {i//chunk_size + 1}/{(len(records)-1)//chunk_size + 1}")
            
            # Validate chunk
            valid_records, invalid_records = self.validate_records(chunk)
            
            # Add invalid results
            for invalid in invalid_records:
                all_results.append(ProcessResult(
                    success=False,
                    data=None,
                    error=invalid['error']
                ))
              if valid_records:
                # Group by operation
                groups = self.group_records_by_operation(valid_records)
                
                # Process each group with optimized algorithms
                if groups['create']:
                    create_results = self.batch_process_creates(groups['create'])
                    all_results.extend(create_results)
                
                if groups['update']:
                    update_results = self.batch_process_updates(groups['update'])
                    all_results.extend(update_results)
                
                if groups['delete']:
                    delete_results = self.batch_process_deletes(groups['delete'])
                    all_results.extend(delete_results)
                if groups['update']:
                    update_results = self.batch_process_updates(groups['update'])
                    all_results.extend(update_results)
                
                if groups['delete']:
                    delete_results = self.batch_process_deletes(groups['delete'])
                    all_results.extend(delete_results)
            
            # Force garbage collection after each chunk
            import gc
            gc.collect()
        
        return all_results
    
    def optimize_create_apm_lead(self, records: List) -> List[Dict]:
        """
        Main Optimization Algorithm
        Thuật toán chính tối ưu hóa create_apm_lead
        """
        start_time = time.time()
        logger.info(f"Starting optimized processing of {len(records)} records")
        
        try:
            # Process in chunks for memory optimization
            results = self.process_in_chunks(records)
            
            # Convert ProcessResult to expected format
            formatted_results = []
            for result in results:
                if result.success:
                    formatted_results.append({
                        "status": "success",
                        "response": 'ok',
                        **result.data
                    })
                else:
                    formatted_results.append({
                        "status": "error",
                        "response": result.error
                    })
            
            total_time = time.time() - start_time
            success_count = sum(1 for r in results if r.success)
            
            logger.info(f"Optimization completed: {success_count}/{len(records)} successful "
                       f"in {total_time:.2f}s")
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Optimization failed: {str(e)}")
            raise


# Usage example:
def optimized_create_apm_lead(records, fastapi):
    """
    Optimized version of create_apm_lead function
    """
    optimizer = APMLeadOptimizer(fastapi)
    return optimizer.optimize_create_apm_lead(records)
