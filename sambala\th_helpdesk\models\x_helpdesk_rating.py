from odoo import models, fields, api


class HelpdeskRating(models.Model):
    _name = 'x.helpdesk.rating'
    _description = 'Helpdesk Rating'

    x_rating = fields.Selection([('1', 'Excellent'),
                                 ('2', 'Good'),
                                 ('3', 'Satisfactory'),
                                 ('4', 'Not good'),
                                 ('5', 'Not Satisfied')], string='Satisfied rating')
    x_comment = fields.Html('Comment')
    x_helpdesk_ticket_id = fields.Many2one('helpdesk.ticket', 'Helpdesk ticket')
    th_team_id = fields.Many2one('helpdesk.team', string='Helpdesk Team')
    th_user_id = fields.Many2one('res.users', string='Assigned to')
    th_readonly = fields.Boolean(default=False, string='🛒')

    def _get_next_stage(self, team_id):
        """Lấy stage tiếp theo dựa trên team_id"""
        return self.env['helpdesk.stage'].search([
            ('team_ids', 'in', team_id)
        ], order="sequence desc", limit=1)

    def _update_ticket_stage(self, ticket_id, next_stage):
        """Cập nhật stage của ticket"""
        if next_stage:
            self.env['helpdesk.ticket'].browse(ticket_id).sudo().write({
                'stage_id': next_stage.id
            })

    @api.model_create_multi
    def create(self, values_list):
        # Xử lý cho trường hợp tạo một bản ghi
        if len(values_list) == 1:
            ticket_id = self._context.get('default_x_helpdesk_ticket_id')
            if ticket_id:
                ticket = self.env['helpdesk.ticket'].browse(ticket_id)
                values = values_list[0]
                
                # Lấy thông tin team và user từ ticket
                values.update({
                    'th_team_id': ticket.team_id.id,
                    'th_user_id': ticket.th_user_id.id
                })

                # Kiểm tra rating và cập nhật stage
                if values.get('x_rating') in ['1', '2', '3']:
                    next_stage = self._get_next_stage(ticket.team_id.id)
                    self._update_ticket_stage(ticket_id, next_stage)

        for values in values_list:
            values['th_readonly'] = True

        return super().create(values_list)

    def write(self, values):
        res = super().write(values)
        if 'x_rating' in values and values.get('x_rating') in ['1', '2', '3']:
            for record in self:
                next_stage = self._get_next_stage(record.x_helpdesk_ticket_id.team_id.id)
                self._update_ticket_stage(record.x_helpdesk_ticket_id.id, next_stage)
        return res
